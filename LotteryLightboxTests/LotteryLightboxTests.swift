//
//  LotteryLightboxTests.swift
//  LotteryLightboxTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 6/30/25.
//

import Foundation
import Testing
@testable import LotteryLightbox

struct LotteryLightboxTests {

    @Test func testPowerballServiceFetchesData() async throws {
        let service = PowerballService()

        do {
            let drawingInfo = try await service.fetchNextDrawingInfo()

            // Verify that we got valid data
            #expect(drawingInfo.estimatedJackpot > 0, "Jackpot should be greater than 0")
            #expect(drawingInfo.cashValue > 0, "Cash value should be greater than 0")
            #expect(drawingInfo.date > Date().addingTimeInterval(-86400), "Date should be recent (within last day)")

            print("Drawing Date: \(drawingInfo.date)")
            print("Estimated Jackpot: $\(drawingInfo.estimatedJackpot)")
            print("Cash Value: $\(drawingInfo.cashValue)")

        } catch {
            print("Error fetching Powerball data: \(error)")
            throw error
        }
    }

}
