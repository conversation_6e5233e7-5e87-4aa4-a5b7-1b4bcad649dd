//
//  PowerballServiceTest.swift
//  LotteryLightboxTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/1/25.
//

import Foundation
import Testing
import SwiftSoup
@testable import LotteryLightbox

class PowerballServiceTest {
    @Test func testPowerballServiceWithMockData() async throws {
        // Load HTML from file
        let testBundle = Bundle(for: type(of: self))
        guard let htmlPath = testBundle.path(forResource: "powerball", ofType: "html"),
              let mockHTML = try? String(contentsOfFile: htmlPath, encoding: .utf8) else {
            throw NSError(domain: "TestError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Could not load powerball.html file"])
        }

        let service = PowerballService(response: mockHTML)
        let drawingInfo = try await service.fetchNextDrawingInfo()

        #expect(drawingInfo.estimatedJackpot == "$174 Million")
        #expect(drawingInfo.cashValue == "$79.7 Million")
    }

    @Test func testPowerballServiceErrorHandling() async throws {
        let invalidHTML = "<html><body><p>No lottery data here</p></body></html>"

        let service = PowerballService(response: invalidHTML)
        
        await #expect(throws: LotterServiceError.parsingError) {
            try await service.fetchNextDrawingInfo()
        }
    }
}
