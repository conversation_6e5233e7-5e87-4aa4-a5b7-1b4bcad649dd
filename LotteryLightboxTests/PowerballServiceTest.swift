//
//  PowerballServiceTest.swift
//  LotteryLightboxTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/1/25.
//

import Foundation
import Testing
@testable import LotteryLightbox

struct PowerballServiceTest {
    @Test func testPowerballServiceFetchesData() async throws {
        let service = PowerballService()

        do {
            let drawingInfo = try await service.fetchNextDrawingInfo()

            // Verify that we got valid data
            #expect(!drawingInfo.estimatedJackpot.isEmpty, "Jackpot should not be empty")
            #expect(!drawingInfo.cashValue.isEmpty, "Cash value should not be empty")
            #expect(drawingInfo.date > Date().addingTimeInterval(-86400 * 7), "Date should be within the last week")

            print("Drawing Date: \(drawingInfo.date)")
            print("Estimated Jackpot: \(drawingInfo.estimatedJackpot)")
            print("Cash Value: \(drawingInfo.cashValue)")

        } catch {
            print("Error fetching Powerball data: \(error)")
            throw error
        }
    }
}
