//
//  ContentView.swift
//  LotteryLightbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 6/30/25.
//

import SwiftUI

struct ContentView: View {
    @State private var drawingInfo: DrawingInfo?
    @State private var isLoading = false
    @State private var errorMessage: String?

    private let powerballService = PowerballService()

    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // Header
                VStack(spacing: 10) {
                    Image(systemName: "circle.fill")
                        .font(.system(size: 60))
                        .foregroundStyle(.red, .white)

                    Text("Powerball")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                }

                // Main Content
                if isLoading {
                    VStack(spacing: 20) {
                        ProgressView()
                            .scaleEffect(1.5)
                        Text("Loading lottery information...")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxHeight: .infinity)
                } else if let error = errorMessage {
                    VStack(spacing: 20) {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 50))
                            .foregroundColor(.orange)

                        Text("Unable to load lottery data")
                            .font(.headline)

                        Text(error)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)

                        Button("Try Again") {
                            fetchDrawingInfo()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .frame(maxHeight: .infinity)
                } else if let info = drawingInfo {
                    LotteryInfoView(drawingInfo: info)
                } else {
                    VStack(spacing: 20) {
                        Image(systemName: "questionmark.circle")
                            .font(.system(size: 50))
                            .foregroundColor(.gray)

                        Text("No lottery data available")
                            .font(.headline)
                            .foregroundColor(.secondary)

                        Button("Load Data") {
                            fetchDrawingInfo()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .frame(maxHeight: .infinity)
                }

                Spacer()
            }
            .padding()
            .navigationTitle("Lottery Lightbox")
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Button("Refresh") {
                        fetchDrawingInfo()
                    }
                    .disabled(isLoading)
                }
            }
        }
        .onAppear {
            fetchDrawingInfo()
        }
    }

    private func fetchDrawingInfo() {
        isLoading = true
        errorMessage = nil

        Task {
            do {
                let info = try await powerballService.fetchNextDrawingInfo()
                await MainActor.run {
                    self.drawingInfo = info
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = error.localizedDescription
                    self.isLoading = false
                }
            }
        }
    }
}

struct LotteryInfoView: View {
    let drawingInfo: DrawingInfo

    var body: some View {
        VStack(spacing: 25) {
            // Next Drawing Date
            VStack(spacing: 8) {
                Text("Next Drawing")
                    .font(.headline)
                    .foregroundColor(.secondary)

                Text(drawingInfo.date, style: .date)
                    .font(.title2)
                    .fontWeight(.semibold)

                Text(drawingInfo.date, style: .time)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)

            // Jackpot Information
            VStack(spacing: 20) {
                // Estimated Jackpot
                VStack(spacing: 8) {
                    Text("Estimated Jackpot")
                        .font(.headline)
                        .foregroundColor(.secondary)

                    Text(drawingInfo.estimatedJackpot)
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                .frame(maxWidth: .infinity)
                .background(
                    LinearGradient(
                        colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(16)

                // Cash Value
                VStack(spacing: 8) {
                    Text("Cash Value")
                        .font(.headline)
                        .foregroundColor(.secondary)

                    Text(drawingInfo.cashValue)
                        .font(.system(size: 28, weight: .semibold, design: .rounded))
                        .foregroundColor(.primary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                .frame(maxWidth: .infinity)
                .background(
                    LinearGradient(
                        colors: [Color.green.opacity(0.1), Color.mint.opacity(0.1)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .cornerRadius(16)
            }

            // Disclaimer
            Text("Lottery numbers are drawn randomly. Please play responsibly.")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.top)
        }
        .padding()
    }
}

#Preview {
    ContentView()
}
