//
//  PowerballService.swift
//  LotteryLightbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/1/25.
//

import Foundation
import SwiftSoup

class PowerballService: LotterServiceProtocol {
    func fetchNextDrawingInfo() async throws -> DrawingInfo {
        guard let url = URL(string: "https://www.powerball.com") else {
            throw LotterServiceError.invalidURL
        }

        let (data, _) = try await URLSession.shared.data(from: url)
        guard let html = String(data: data, encoding: .utf8) else {
            throw LotterServiceError.noData
        }

        do {
            let doc = try SwiftSoup.parse(html)

            // Find the next drawing section
            guard let nextDrawingSection = try doc.select("h4:contains(Next Drawing)").first()?.parent() else {
                throw LotterServiceError.elementNotFound
            }

            // Parse the date - look for the date text in the next drawing section
            let dateText = try nextDrawingSection.select("h5").first()?.text() ?? ""
            let drawingDate = parseDrawingDate(from: dateText)

            // Parse jackpot amount - look for text containing "Estimated Jackpot"
            let jackpotText = try nextDrawingSection.select("*:contains(Estimated Jackpot)").first()?.text() ?? ""
            let jackpot = parseAmount(from: jackpotText)

            // Parse cash value - look for text containing "Cash Value"
            let cashValueText = try nextDrawingSection.select("*:contains(Cash Value)").first()?.text() ?? ""
            let cashValue = parseAmount(from: cashValueText)

            return DrawingInfo(date: drawingDate,
                               estimatedJackpot: jackpot,
                               cashValue: cashValue)

        } catch {
            throw LotterServiceError.parsingError
        }
    }

    private func parseDrawingDate(from text: String) -> Date {
        // Expected format: "Wed, Jul 2, 2025"
        let formatter = DateFormatter()
        formatter.dateFormat = "E, MMM d, yyyy"
        formatter.locale = Locale(identifier: "en_US")

        return formatter.date(from: text) ?? Date()
    }

    private func parseAmount(from text: String) -> Int {
        // Extract numbers from text like "Estimated Jackpot $174 Million" or "Cash Value $79.7 Million"
        let cleanedText = text.replacingOccurrences(of: ",", with: "")

        // Use regex to find dollar amounts
        let pattern = #"\$([0-9]+(?:\.[0-9]+)?)\s*(Million|Billion)?"#
        guard let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive),
              let match = regex.firstMatch(in: cleanedText, options: [], range: NSRange(location: 0, length: cleanedText.count)) else {
            return 0
        }

        let amountRange = Range(match.range(at: 1), in: cleanedText)!
        let amountString = String(cleanedText[amountRange])

        guard let amount = Double(amountString) else { return 0 }

        // Check for Million or Billion multiplier
        let multiplierRange = match.range(at: 2)
        if multiplierRange.location != NSNotFound,
           let multiplierRange = Range(multiplierRange, in: cleanedText) {
            let multiplier = String(cleanedText[multiplierRange]).lowercased()
            if multiplier == "billion" {
                return Int(amount * 1_000_000_000)
            } else if multiplier == "million" {
                return Int(amount * 1_000_000)
            }
        }

        return Int(amount)
    }
}
 
