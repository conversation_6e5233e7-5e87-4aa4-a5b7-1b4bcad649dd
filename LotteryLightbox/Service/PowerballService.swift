//
//  PowerballService.swift
//  LotteryLightbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/1/25.
//

import Foundation
import SwiftSoup

class PowerballService: LotterServiceProtocol {
    var response: String?
    
    init(response: String? = nil) {
        self.response = response
    }
    
    func fetchNextDrawingInfo() async throws -> DrawingInfo {
        if response == nil {
            guard let url = URL(string: "https://www.powerball.com") else {
                throw LotterServiceError.invalidURL
            }

            let (data, _) = try await URLSession.shared.data(from: url)
            response = String(data: data, encoding: .utf8)
        }
        
        guard let html = response else {
            throw LotterServiceError.noData
        }
        
        do {
            let doc = try SwiftSoup.parse(html)

            // Find the next drawing section using specific ID
            guard let nextDrawingSection = try doc.select("div#next-drawing").first() else {
                throw LotterServiceError.parsingError
            }

            // Parse the date using the specific ID and data attribute
            let dateString = try nextDrawingSection.select("div#nextDraw").attr("data-drawdateutc")
            let drawingDate = parseDrawingDate(from: dateString)

            // Parse jackpot amount - find all spans with game-jackpot-number class and get the first one (jackpot)
            let jackpotSpans = try nextDrawingSection.select("span.game-jackpot-number")
            let jackpotText = try jackpotSpans.first()?.text() ?? ""

            // Parse cash value - find all spans with game-jackpot-number class and get the second one (cash value)
            let cashValueText = try jackpotSpans.count > 1 ? jackpotSpans[1].text() : ""

            return DrawingInfo(date: drawingDate,
                               estimatedJackpot: jackpotText,
                               cashValue: cashValueText)

        } catch {
            throw LotterServiceError.parsingError
        }
    }

    private func parseDrawingDate(from dateString: String) -> Date {
        // Expected format: "2025-07-03T02:59:00.0000000Z"
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]

        return formatter.date(from: dateString) ?? Date()
    }
}
 
