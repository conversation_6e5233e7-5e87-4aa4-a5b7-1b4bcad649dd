//
//  PowerballService.swift
//  LotteryLightbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/1/25.
//

import Foundation
import SwiftSoup

class PowerballService: LotterServiceProtocol {
    func fetchNextDrawingInfo() async throws -> DrawingInfo {
        guard let url = URL(string: "https://www.powerball.com") else {
            throw LotterServiceError.invalidURL
        }

        let (data, _) = try await URLSession.shared.data(from: url)
        guard let html = String(data: data, encoding: .utf8) else {
            throw LotterServiceError.noData
        }

        do {
            let doc = try SwiftSoup.parse(html)

            guard let jackpotContainer = try doc.select("div#next-drawing").first() else {
                throw LotterServiceError.elementNotFound
            }

            let date = try jackpotContainer.select("div#nextDraw").attr("data-drawdateutc") // 2025-07-03T02:59:00.0000000Z
            let jackpot = 0
            let cashValue = 0

            return DrawingInfo(date: date,
                               estimatedJackpot: jackpot,
                               cashValue: cashValue)

        } catch {
            throw LotterServiceError.parsingError
        }
    }
}
 
