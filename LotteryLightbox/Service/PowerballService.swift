//
//  PowerballService.swift
//  LotteryLightbox
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 7/1/25.
//

import Foundation
import SwiftSoup

class PowerballService: LotterServiceProtocol {
    func fetchNextDrawingInfo() async throws -> DrawingInfo {
        guard let url = URL(string: "https://www.powerball.com") else {
            throw LotterServiceError.invalidURL
        }

        let (data, _) = try await URLSession.shared.data(from: url)
        guard let html = String(data: data, encoding: .utf8) else {
            throw LotterServiceError.noData
        }

        do {
            let doc = try SwiftSoup.parse(html)

            // Find the next drawing section using specific ID
            guard let nextDrawingSection = try doc.select("div#next-drawing").first() else {
                throw LotterServiceError.elementNotFound
            }

            // Parse the date using the specific ID and data attribute
            let dateString = try nextDrawingSection.select("div#nextDraw").attr("data-drawdateutc")
            let drawingDate = parseDrawingDate(from: dateString)

            // Parse jackpot amount - look for text containing "Estimated Jackpot"
            let jackpotText = try nextDrawingSection.select("*:contains(Estimated Jackpot)").first()?.text() ?? ""

            // Parse cash value - look for text containing "Cash Value"
            let cashValueText = try nextDrawingSection.select("*:contains(Cash Value)").first()?.text() ?? ""

            return DrawingInfo(date: drawingDate,
                               estimatedJackpot: jackpotText,
                               cashValue: cashValueText)

        } catch {
            throw LotterServiceError.parsingError
        }
    }

    private func parseDrawingDate(from dateString: String) -> Date {
        // Expected format: "2025-07-03T02:59:00.0000000Z"
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]

        return formatter.date(from: dateString) ?? Date()
    }
}
 
