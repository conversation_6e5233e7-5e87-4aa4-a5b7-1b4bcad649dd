// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		552775302E13C336008C9EC2 /* SwiftSoup in Frameworks */ = {isa = PBXBuildFile; productRef = 5527752F2E13C336008C9EC2 /* SwiftSoup */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		552775122E13B89C008C9EC2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 552774F92E13B89A008C9EC2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 552775002E13B89A008C9EC2;
			remoteInfo = LotteryLightbox;
		};
		5527751C2E13B89C008C9EC2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 552774F92E13B89A008C9EC2 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 552775002E13B89A008C9EC2;
			remoteInfo = LotteryLightbox;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		552775012E13B89A008C9EC2 /* LotteryLightbox.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = LotteryLightbox.app; sourceTree = BUILT_PRODUCTS_DIR; };
		552775112E13B89C008C9EC2 /* LotteryLightboxTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = LotteryLightboxTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		5527751B2E13B89C008C9EC2 /* LotteryLightboxUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = LotteryLightboxUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		552775032E13B89A008C9EC2 /* LotteryLightbox */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LotteryLightbox;
			sourceTree = "<group>";
		};
		552775142E13B89C008C9EC2 /* LotteryLightboxTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LotteryLightboxTests;
			sourceTree = "<group>";
		};
		5527751E2E13B89C008C9EC2 /* LotteryLightboxUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LotteryLightboxUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		552774FE2E13B89A008C9EC2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				552775302E13C336008C9EC2 /* SwiftSoup in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5527750E2E13B89C008C9EC2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		552775182E13B89C008C9EC2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		552774F82E13B89A008C9EC2 = {
			isa = PBXGroup;
			children = (
				552775032E13B89A008C9EC2 /* LotteryLightbox */,
				552775142E13B89C008C9EC2 /* LotteryLightboxTests */,
				5527751E2E13B89C008C9EC2 /* LotteryLightboxUITests */,
				552775022E13B89A008C9EC2 /* Products */,
			);
			sourceTree = "<group>";
		};
		552775022E13B89A008C9EC2 /* Products */ = {
			isa = PBXGroup;
			children = (
				552775012E13B89A008C9EC2 /* LotteryLightbox.app */,
				552775112E13B89C008C9EC2 /* LotteryLightboxTests.xctest */,
				5527751B2E13B89C008C9EC2 /* LotteryLightboxUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		552775002E13B89A008C9EC2 /* LotteryLightbox */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 552775252E13B89C008C9EC2 /* Build configuration list for PBXNativeTarget "LotteryLightbox" */;
			buildPhases = (
				552774FD2E13B89A008C9EC2 /* Sources */,
				552774FE2E13B89A008C9EC2 /* Frameworks */,
				552774FF2E13B89A008C9EC2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				552775032E13B89A008C9EC2 /* LotteryLightbox */,
			);
			name = LotteryLightbox;
			packageProductDependencies = (
				5527752F2E13C336008C9EC2 /* SwiftSoup */,
			);
			productName = LotteryLightbox;
			productReference = 552775012E13B89A008C9EC2 /* LotteryLightbox.app */;
			productType = "com.apple.product-type.application";
		};
		552775102E13B89C008C9EC2 /* LotteryLightboxTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 552775282E13B89C008C9EC2 /* Build configuration list for PBXNativeTarget "LotteryLightboxTests" */;
			buildPhases = (
				5527750D2E13B89C008C9EC2 /* Sources */,
				5527750E2E13B89C008C9EC2 /* Frameworks */,
				5527750F2E13B89C008C9EC2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				552775132E13B89C008C9EC2 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				552775142E13B89C008C9EC2 /* LotteryLightboxTests */,
			);
			name = LotteryLightboxTests;
			packageProductDependencies = (
			);
			productName = LotteryLightboxTests;
			productReference = 552775112E13B89C008C9EC2 /* LotteryLightboxTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		5527751A2E13B89C008C9EC2 /* LotteryLightboxUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 5527752B2E13B89C008C9EC2 /* Build configuration list for PBXNativeTarget "LotteryLightboxUITests" */;
			buildPhases = (
				552775172E13B89C008C9EC2 /* Sources */,
				552775182E13B89C008C9EC2 /* Frameworks */,
				552775192E13B89C008C9EC2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				5527751D2E13B89C008C9EC2 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				5527751E2E13B89C008C9EC2 /* LotteryLightboxUITests */,
			);
			name = LotteryLightboxUITests;
			packageProductDependencies = (
			);
			productName = LotteryLightboxUITests;
			productReference = 5527751B2E13B89C008C9EC2 /* LotteryLightboxUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		552774F92E13B89A008C9EC2 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					552775002E13B89A008C9EC2 = {
						CreatedOnToolsVersion = 16.4;
					};
					552775102E13B89C008C9EC2 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 552775002E13B89A008C9EC2;
					};
					5527751A2E13B89C008C9EC2 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 552775002E13B89A008C9EC2;
					};
				};
			};
			buildConfigurationList = 552774FC2E13B89A008C9EC2 /* Build configuration list for PBXProject "LotteryLightbox" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 552774F82E13B89A008C9EC2;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				5527752E2E13C336008C9EC2 /* XCRemoteSwiftPackageReference "SwiftSoup" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 552775022E13B89A008C9EC2 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				552775002E13B89A008C9EC2 /* LotteryLightbox */,
				552775102E13B89C008C9EC2 /* LotteryLightboxTests */,
				5527751A2E13B89C008C9EC2 /* LotteryLightboxUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		552774FF2E13B89A008C9EC2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5527750F2E13B89C008C9EC2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		552775192E13B89C008C9EC2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		552774FD2E13B89A008C9EC2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		5527750D2E13B89C008C9EC2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		552775172E13B89C008C9EC2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		552775132E13B89C008C9EC2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 552775002E13B89A008C9EC2 /* LotteryLightbox */;
			targetProxy = 552775122E13B89C008C9EC2 /* PBXContainerItemProxy */;
		};
		5527751D2E13B89C008C9EC2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 552775002E13B89A008C9EC2 /* LotteryLightbox */;
			targetProxy = 5527751C2E13B89C008C9EC2 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		552775232E13B89C008C9EC2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		552775242E13B89C008C9EC2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		552775262E13B89C008C9EC2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = LotteryLightbox/LotteryLightbox.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.LotteryLightbox;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		552775272E13B89C008C9EC2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = LotteryLightbox/LotteryLightbox.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.LotteryLightbox;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		552775292E13B89C008C9EC2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.LotteryLightboxTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/LotteryLightbox.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/LotteryLightbox";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		5527752A2E13B89C008C9EC2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.LotteryLightboxTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/LotteryLightbox.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/LotteryLightbox";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		5527752C2E13B89C008C9EC2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.LotteryLightboxUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = LotteryLightbox;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		5527752D2E13B89C008C9EC2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = NHMD59Z28Z;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = koo.jaesung.LotteryLightboxUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = LotteryLightbox;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		552774FC2E13B89A008C9EC2 /* Build configuration list for PBXProject "LotteryLightbox" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				552775232E13B89C008C9EC2 /* Debug */,
				552775242E13B89C008C9EC2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		552775252E13B89C008C9EC2 /* Build configuration list for PBXNativeTarget "LotteryLightbox" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				552775262E13B89C008C9EC2 /* Debug */,
				552775272E13B89C008C9EC2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		552775282E13B89C008C9EC2 /* Build configuration list for PBXNativeTarget "LotteryLightboxTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				552775292E13B89C008C9EC2 /* Debug */,
				5527752A2E13B89C008C9EC2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		5527752B2E13B89C008C9EC2 /* Build configuration list for PBXNativeTarget "LotteryLightboxUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5527752C2E13B89C008C9EC2 /* Debug */,
				5527752D2E13B89C008C9EC2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		5527752E2E13C336008C9EC2 /* XCRemoteSwiftPackageReference "SwiftSoup" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/scinfu/SwiftSoup";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.8.8;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		5527752F2E13C336008C9EC2 /* SwiftSoup */ = {
			isa = XCSwiftPackageProductDependency;
			package = 5527752E2E13C336008C9EC2 /* XCRemoteSwiftPackageReference "SwiftSoup" */;
			productName = SwiftSoup;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 552774F92E13B89A008C9EC2 /* Project object */;
}
